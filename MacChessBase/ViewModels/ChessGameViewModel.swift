//
//  ChessGameViewModel.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/1.
//

import SwiftUI
import ChessKit
import Combine
import AppKit
import AVFoundation



/// ViewModel that manages the chess game UI interactions
@MainActor
final class ChessGameViewModel: ObservableObject {
    // MARK: - Session Reference
    @ObservedObject var session: GameSession

    // MARK: - Combine Support
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI-only Published Properties (Delegated to DragManager)
    var selectedSquare: Square? {
        get { dragManager.selectedSquare }
        set { dragManager.selectedSquare = newValue }
    }

    var possibleMoves: [Square] {
        dragManager.possibleMoves
    }

    @Published var lastMove: Move?

    // MARK: - Reverse Drag Properties (Delegated to DragManager)
    var isReverseDragActive: Bool {
        dragManager.isReverseDragActive
    }

    var reverseDragTarget: Square? {
        dragManager.reverseDragTarget
    }

    var reverseDragValidSources: [Square] {
        dragManager.reverseDragValidSources
    }
    
    /// Game status - derived from session
    var gameStatus: GameStatus {
        session.gameStatus
    }
    @Published var promotionMove: Move?
    @Published var showPromotionDialog = false


    // MARK: - Navigation Properties (delegated to ChessNavigator)
    // Navigation properties are now accessed directly via viewModel.navigator in Views

    // MARK: - Variation Creation
    @Published var showVariationCreationDialog = false
    @Published var pendingMove: Move?
    @Published var pendingMoveFromIndex: MoveTree.MoveIndex?
    @Published var existingNextMoveIndex: MoveTree.MoveIndex?
    
    // VariationCreationOption is now defined in ChessMoveExecutor

    // MARK: - Move Editing
    @Published var showMoveEditMenu = false
    @Published var editMenuPosition: CGPoint = .zero
    @Published var selectedMoveForEdit: MoveTree.MoveIndex?
    
    // MARK: - Position Editor (delegated to fileOperations)
    var showPositionEditor: Bool {
        get { fileOperations.showPositionEditor }
        set { fileOperations.showPositionEditor = newValue }
    }

    // MARK: - Import Error Alert (delegated to fileOperations)
    var showImportErrorAlert: Bool {
        get { fileOperations.showImportErrorAlert }
        set { fileOperations.showImportErrorAlert = newValue }
    }

    // MARK: - File Operations
    private let fileOperations: ChessFileOperations

    // MARK: - Annotation Manager
    private let annotationManager = ChessAnnotationManager()

    // MARK: - Drag Manager
    @ObservedObject internal var dragManager = ChessDragManager()

    // MARK: - Service References
    private let soundManager = SoundManager.shared
    private let engineManager = EngineManager.shared
    
    // MARK: - Move Executor
    @ObservedObject private var moveExecutor = ChessMoveExecutor()
    
    // MARK: - Navigator
    @ObservedObject var navigator = ChessNavigator()

    // MARK: - Engine State Access (for UI binding)
    var isEngineRunning: Bool { engineManager.state != .stopped }
    var isEngineAnalyzing: Bool { engineManager.state == .analyzing }
    var isEnginePaused: Bool { engineManager.state == .paused }
    var engineState: EngineState { engineManager.state }
    var currentEngineEvaluation: EngineEvaluation? { engineManager.currentEvaluation }
    var engineLines: [EngineLine] { engineManager.engineLines }

    // MARK: - Engine Actions
    func getEngineManager() -> EngineManager { engineManager }

    // MARK: - Move Display Manager
    @ObservedObject private var moveDisplayManager = MoveDisplayManager()

    /// Returns true if only the current move index has changed, not the game content
    var hasOnlyCurrentMoveChanged: Bool {
        return moveDisplayManager.hasOnlyCurrentMoveChanged
    }
    
    
    // MARK: - Enums and Structs
    // GameStatus is now defined in GameSession

    // VariationOption is now defined in ChessNavigator
    
    // MARK: - Initialization
    init(session: GameSession) {
        self.session = session
        self.fileOperations = ChessFileOperations()

        // Debug log to confirm ChessGameViewModel initialization
        print("🎯 ChessGameViewModel initialized with session")
        print("🎯 EngineManager initialized: \(engineManager)")

        // Setup move display manager with session
        moveDisplayManager.updateSession(session)

        // Setup file operations callbacks
        setupFileOperationsCallbacks()

        // Initialize UI state from session
        updateFromSession()

        // Setup session state monitoring
        setupSessionObserver()

        // Setup engine state monitoring
        setupEngineObserver()
        
        // Setup move executor callbacks
        setupMoveExecutorCallbacks()
        
        // Setup navigator callbacks
        setupNavigatorCallbacks()
    }

    /// Sets up callbacks for navigator
    private func setupNavigatorCallbacks() {
        navigator.onNavigationStateChanged = { [weak self] in
            self?.objectWillChange.send()
        }
    }
    
    /// Sets up callbacks for move executor
    private func setupMoveExecutorCallbacks() {
        moveExecutor.onMoveCompleted = { [weak self] move in
            self?.lastMove = move
            self?.clearAllSelections()
            self?.throttledCacheInvalidation()
        }
        
        moveExecutor.onPromotionNeeded = { [weak self] move in
            self?.promotionMove = move
            self?.showPromotionDialog = true
            self?.clearAllSelections()
        }
        
        moveExecutor.onVariationCreationNeeded = { [weak self] move, fromIndex, existingIndex in
            self?.pendingMove = move
            self?.pendingMoveFromIndex = fromIndex
            self?.existingNextMoveIndex = existingIndex
            self?.showVariationCreationDialog = true
            self?.clearAllSelections()
        }
        
        moveExecutor.onGameStatusChanged = { [weak self] move in
            self?.checkGameStatus(move)
        }
    }
    
    /// Sets up callbacks for file operations
    private func setupFileOperationsCallbacks() {
        fileOperations.onStateReset = { [weak self] in
            self?.clearAllSelections()
            self?.promotionMove = nil
            self?.showPromotionDialog = false
            self?.showVariationCreationDialog = false
            self?.navigator.showVariationSelection = false
            self?.navigator.availableVariations = []
            self?.navigator.isKeyboardNavigationDisabled = false
            self?.pendingMove = nil
            self?.pendingMoveFromIndex = nil
            self?.existingNextMoveIndex = nil
        }

        fileOperations.onCacheInvalidate = { [weak self] in
            self?.invalidateCache()
        }
    }

    /// Sets up observers for session state changes
    private func setupSessionObserver() {
        // Monitor currentMoveIndex changes
        session.$currentMoveIndex
            .sink { [weak self] _ in
                self?.updateFromSession()
            }
            .store(in: &cancellables)

        // Monitor game changes (for imports, new games, etc.)
        session.$game
            .sink { [weak self] _ in
                self?.updateFromSession()
            }
            .store(in: &cancellables)

        // Monitor board changes
        session.$board
            .sink { [weak self] _ in
                self?.updateFromSession()
            }
            .store(in: &cancellables)
    }


    /// Sets up observers for engine state changes
    private func setupEngineObserver() {
        // Monitor engine state changes to trigger UI updates
        engineManager.$state
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)

        engineManager.$currentEvaluation
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)

        engineManager.$engineLines
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }

    /// Updates UI state from the observed session
    private func updateFromSession() {
        // Update last move for highlighting
        if session.currentMoveIndex != session.game.startingIndex {
            lastMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
        } else {
            lastMove = nil
        }

        // Clear selections when session state changes
        clearAllSelections()

        // Trigger cache invalidation
        throttledCacheInvalidation()

        // Auto-analyze new position if engine is running and analyzing
        if engineManager.state == .analyzing {
            Task.detached(priority: .background) { [weak self] in
                guard let self = self else { return }
                await self.engineManager.analyzePosition(self.session.board.position)
            }
        }
    }
    
    // MARK: - Game Actions
    
    /// Handles square selection and piece movement
    func handleSquarePress(_ square: Square) {
        if let selectedSquare = selectedSquare {
            // Normal mode with a piece already selected
            attemptMove(from: selectedSquare, to: square)
        } else {
            // No selection, try to select a piece
            selectSquare(square)
        }
    }
    
    /// Selects a square if it contains a piece of the current player
    private func selectSquare(_ square: Square) {
        dragManager.selectSquare(square, in: session)
    }
    
    /// Attempts to move a piece from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        let result = moveExecutor.attemptMove(from: startSquare, to: endSquare, in: session)
        
        switch result {
        case .success(_):
            // Move completed successfully, callbacks will handle UI updates
            break
            
        case .invalidMove:
            clearAllSelections()
            
        case .shouldDeselect:
            clearAllSelections()
            
        case .shouldReselect(let square):
            selectSquare(square)
            
        case .variationDialogNeeded:
            // Dialog will be shown via callback
            break
        }
    }
    
    // handleMoveResult is now handled by ChessMoveExecutor callbacks

    // completeMoveSequence is now handled by ChessMoveExecutor
    
    /// Executes a move with the specified variation creation option
    private func executeMove(_ move: Move, option: VariationCreationOption) {
        let fromIndex = pendingMoveFromIndex ?? session.currentMoveIndex
        
        let success = moveExecutor.executeMove(move, option: option, from: fromIndex, in: session)
        
        if !success {
            clearAllSelections()
        }
        
        // Clear pending move data
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
    }
    
    /// Completes a pawn promotion
    func completePromotion(to pieceKind: Piece.Kind) {
        guard let promotionMove = promotionMove else { return }
        
        if let completedMove = moveExecutor.completePromotion(move: promotionMove, to: pieceKind, in: session) {
            lastMove = completedMove
        }
        
        self.promotionMove = nil
        showPromotionDialog = false
        
        clearAllSelections()
        throttledCacheInvalidation()
    }
    
    /// Clears the current selection
    func clearSelection() {
        dragManager.clearSelection()
    }

    /// Clears all selections (normal and reverse drag)
    func clearAllSelections() {
        dragManager.clearAllSelections()
    }
    
    // MARK: - Drag and Drop Validation (Delegated to DragManager)

    /// Validates if a drag operation can start for the given piece and square
    func validateDragStart(piece: Piece, from square: Square) -> Bool {
        return dragManager.validateDragStart(piece: piece, from: square, in: session)
    }

    /// Checks if a piece at the given square can be moved (has legal moves)
    func canMovePiece(at square: Square) -> Bool {
        return dragManager.canMovePiece(at: square, in: session)
    }

    /// Sets the selected square for move validation
    func setSelectedSquare(_ square: Square) {
        dragManager.setSelectedSquare(square, in: session)
    }
    
    /// Attempts to move a piece from one square to another (called from drag operations)
//    func attemptMove(from startSquare: Square, to endSquare: Square) {
//        attemptMove(from: startSquare, to: endSquare)
//    }
    
    /// Checks the game status after a move
    private func checkGameStatus(_ move: Move) {
        // gameStatus is now computed from session automatically
        // This method can be simplified or removed if no other logic is needed
    }
    
    // MARK: - Game Management
    
    /// Resets the game to the starting position
    func newGame() {
        session.newGame()
        clearSelection()
        promotionMove = nil
        showPromotionDialog = false
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        session.currentFilePath = nil // Clear file path for new game
        invalidateCache()
    }
    
    /// Gets the current player's turn
    var currentPlayer: Piece.Color {
        session.board.position.sideToMove
    }
    
    /// Gets the game's PGN representation
    var pgn: String {
        session.pgn
    }
    
    // MARK: - File Management (delegated to ChessFileOperations)

    /// Loads a game from PGN
    func loadGame(from pgn: String) {
        fileOperations.loadGame(from: pgn, into: session)
    }

    /// Loads a game from PGN file
    func loadGame(from url: URL) {
        fileOperations.loadGame(from: url, into: session)
    }

    /// Loads a game from a Game object
    func loadGameFromObject(_ gameObject: Game) {
        fileOperations.loadGameFromObject(gameObject, into: session)
    }

    /// Loads a position from FEN string
    func loadPosition(from fen: String) {
        fileOperations.loadPosition(from: fen, into: session)
    }

    /// Saves the current game to the current file path
    func saveGame() throws {
        try fileOperations.saveGame(from: session)
    }

    /// Saves the current game to a new file path
    func saveGame(to url: URL) throws {
        try fileOperations.saveGame(to: url, from: session)
    }

    /// Loads game from clipboard
    func loadGameFromClipboard() {
        if !fileOperations.loadGameFromClipboard(into: session) {
            showImportErrorAlert = true
        }
    }

    /// Opens the position editor
    func openPositionEditor() {
        fileOperations.openPositionEditor()
    }

    /// Handles setting a new position from the position editor
    func setPosition(_ position: Position, shouldFlip: Bool = false) {
        fileOperations.setPosition(position, shouldFlip: shouldFlip, in: session)
    }
}

/// MARK: - Move Editing Extension
extension ChessGameViewModel {

    /// Shows the edit menu for a specific move
    func showEditMenu(for moveIndex: MoveTree.MoveIndex, at position: CGPoint) {
        // Defer state updates to avoid "publishing changes from within view updates" warning
        Task { @MainActor in
            self.selectedMoveForEdit = moveIndex
            self.editMenuPosition = position
            self.showMoveEditMenu = true
        }
    }

    /// Hides the edit menu
    func hideEditMenu() {
        // Defer state updates to avoid "publishing changes from within view updates" warning
        Task { @MainActor in
            self.showMoveEditMenu = false
            self.selectedMoveForEdit = nil
        }
    }

    /// Deletes a move and all subsequent moves
    func deleteMove(at index: MoveTree.MoveIndex) {
        guard session.deleteMove(at: index) == true else {
            return
        }
        
        invalidateCache()
        hideEditMenu()
    }
    
    func deleteVariation(at index: MoveTree.MoveIndex) {
        if let rootNodeIndex = session.game.moves.getVariationRootNodeIndex(index: index) {
            deleteMove(at: rootNodeIndex)
        }
    }
    
    /// Deletes all moves before the specified move, making it the new starting position
    func deleteBeforeMove(at index: MoveTree.MoveIndex) {
        guard session.deleteBeforeMove(at: index) == true else {
            return
        }
        
        invalidateCache()
        hideEditMenu()
    }

    /// Promotes a variation's priority
    func promoteVariation(at index: MoveTree.MoveIndex) {
        guard session.promoteVariation(at: index) == true else {
            return
        }

        invalidateCache()
        hideEditMenu()
    }

    /// Promotes a variation to main variation
    func promoteToMainVariation(at index: MoveTree.MoveIndex) {
        guard session.promoteToMainVariation(at: index) == true else {
            return
        }

        invalidateCache()
        hideEditMenu()
    }

    /// Finds the last valid position before a given index
    private func findLastValidPosition(before index: MoveTree.MoveIndex) -> MoveTree.MoveIndex? {
        let history = session.game.moves.history(for: index)

        // Go through history in reverse to find the last valid position
        for historyIndex in history.reversed() {
            if session.game.positions[historyIndex] != nil {
                return historyIndex
            }
        }

        return session.game.startingIndex
    }


    /// Checks if a variation can be promoted (not already main variation)
    func canPromoteVariation(at index: MoveTree.MoveIndex) -> Bool {
        return !session.game.moves.isOnMainVariation(index: index)
    }
}

/// MARK: - Move Navigation Extension
extension ChessGameViewModel {
    
    /// Goes to the previous move in the game
    func goToPreviousMove() {
        navigator.goToPreviousMove(in: session)
    }
    
    /// Goes to the next move in the game
    func goToNextMove() {
        navigator.goToNextMove(in: session)
    }
    
    // showVariationSelectionDialog is now handled by ChessNavigator
    
    // generatePGNTextForMove is now handled by ChessNavigator
    
    /// Selects a variation and navigates to it
    func selectVariation(_ option: VariationOption) {
        navigator.selectVariation(option, in: session)
    }
    
    /// Cancels variation selection
    func cancelVariationSelection() {
        navigator.cancelVariationSelection()
    }
    
    /// Goes to the start of the game
    func goToStart() {
        navigator.goToStart(in: session)
    }
    
    /// Goes to the end of the game (last move played)
    func goToEnd() {
        navigator.goToEnd(in: session)
    }
    
    /// Goes to a specific move index
    func goToMove(at index: MoveTree.MoveIndex) {
        navigator.goToMove(at: index, in: session)
    }
    
    /// Updates the board position to match the current move index (deprecated - handled by GameSession)
    @MainActor
    private func updateBoardToCurrentIndex() async {
        // This method is deprecated - GameSession handles board updates automatically
        updateFromSession()
    }
    
    /// Helper method to find a move from position differences
    private func findMoveFromPositions(from previousPosition: Position, to currentPosition: Position) {
        // Compare piece positions to find the move
        let previousPieces = previousPosition.pieces
        let currentPieces = currentPosition.pieces
        
        // Find pieces that moved
        for piece in previousPieces {
            if !currentPieces.contains(where: { $0.square == piece.square && $0.kind == piece.kind && $0.color == piece.color }) {
                // This piece moved, find where it went
                if let movedPiece = currentPieces.first(where: { $0.kind == piece.kind && $0.color == piece.color && !previousPieces.contains(where: { $0.square == $0.square && $0.kind == piece.kind && $0.color == piece.color }) }) {
                    // Create a move object for highlighting
                    let move = Move(metaMove: MetaMove(
                        result: .move,
                        piece: piece,
                        start: piece.square,
                        end: movedPiece.square
                    ))
                    lastMove = move
                    return
                }
            }
        }
    }
    
    /// Gets all moves in the game for display in the notation view (cached for performance)
    var cachedMoves: [MoveDisplayItem] {
        return moveDisplayManager.cachedMoves
    }

    /// Invalidates the move cache
    private func invalidateCache() {
        moveDisplayManager.invalidateCache()
    }

    #if DEBUG
    internal func invalidateCacheTest() {
        moveDisplayManager.invalidateCacheTest()
    }
    #endif

    /// Throttled cache invalidation to prevent excessive recalculation during rapid moves
    private func throttledCacheInvalidation() {
        moveDisplayManager.invalidateCache()
    }
    

    
    /// Checks if we can go to the previous move
    var canGoToPreviousMove: Bool {
        return navigator.canGoToPreviousMove(in: session)
    }
    
    /// Checks if we can go to the next move
    var canGoToNextMove: Bool {
        return navigator.canGoToNextMove(in: session)
    }
    
    var currentPosition: Position? {
        return session.currentPosition
    }
    
    /// Gets the current move number for display
    var currentMoveNumber: String {
        if session.currentMoveIndex == session.game.startingIndex {
            return "Start"
        }
        
        let moveNumber = session.game.moves.getNodeNumber(index: session.currentMoveIndex)!
        let color = session.game.moves.getNodeColor(index: session.currentMoveIndex)!
        return color == .white ? "\(moveNumber)." : "\(moveNumber)…"
    }
    
    /// Handles variation creation option selection
    func selectVariationCreationOption(_ option: VariationCreationOption) {
        guard let move = pendingMove else { return }

        // Defer state updates to avoid "publishing changes from within view updates" warning
        Task { @MainActor in
            self.showVariationCreationDialog = false
            self.navigator.isKeyboardNavigationDisabled = false  // Re-enable navigation

            // Add a small delay to ensure UI is fully reset after sheet dismissal
            Task { @MainActor in
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                self.executeMove(move, option: option)
            }
        }
    }
    
    /// Cancels variation creation dialog
    func cancelVariationCreation() {
        // Defer state updates to avoid "publishing changes from within view updates" warning
        Task { @MainActor in
            self.showVariationCreationDialog = false
            self.navigator.isKeyboardNavigationDisabled = false  // Re-enable navigation
            self.pendingMove = nil
            self.pendingMoveFromIndex = nil
            self.existingNextMoveIndex = nil
            
            // Restore the board to the current move index position
            // This ensures the board shows the correct state after cancellation
            Task {
                await self.updateBoardToCurrentIndex()
            }
        }
    }
    
    /// Finds all squares containing pieces that can move to the target square
    private func findSourceSquares(for targetSquare: Square) -> [Square] {
        return dragManager.findSourceSquares(for: targetSquare, in: session)
    }
    
    /// Starts a reverse drag from a target square
    func startReverseDrag(from targetSquare: Square) -> Bool {
        return dragManager.startReverseDrag(from: targetSquare, in: session)
    }
    
    /// Completes a reverse drag to a source square
    func completeReverseDrag(to sourceSquare: Square) {
        dragManager.completeReverseDrag(to: sourceSquare, in: session) { [weak self] from, to in
            self?.attemptMove(from: from, to: to)
        }
    }
    
    /// Cancels the reverse drag operation
    func cancelReverseDrag() {
        dragManager.cancelReverseDrag()
    }
    
    // shouldShowVariationCreationDialog is now handled by ChessMoveExecutor
    
    // getExistingNextMoveIndex is now handled by ChessMoveExecutor
    
    // MARK: - PGN Metadata
    
    /// Formats player information with rating if available
    func formatPlayerInfo(name: String, color: Piece.Color) -> String {
        if name.isEmpty {
            return color == .white ? "White" : "Black"
        }

        // Extract rating from name if it contains rating info
        // Format: "Player Name (2500)" or "Player Name"
        let ratingPattern = #"\((\d+)\)"#
        if let ratingMatch = name.range(of: ratingPattern, options: .regularExpression) {
            let rating = String(name[ratingMatch]).replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "")
            let nameWithoutRating = name.replacingOccurrences(of: ratingPattern, with: "", options: .regularExpression).trimmingCharacters(in: .whitespaces)
            return "\(nameWithoutRating) \(rating)"
        }

        // Check for rating in other tags
        let ratingKey = color == .white ? "WhiteElo" : "BlackElo"
        if let rating = session.game.tags.other[ratingKey], !rating.isEmpty {
            return "\(name) \(rating)"
        }

        return name
    }
    
    // MARK: - Assessment Methods (Delegated to AnnotationManager)

    /// Sets the move assessment for a move at the given index
    func setMoveAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex) {
        annotationManager.setMoveAssessment(assessment, at: moveIndex, in: session)
    }
    
    /// Sets the position assessment for a move at the given index
    func setPositionAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex) {
        annotationManager.setPositionAssessment(assessment, at: moveIndex, in: session)
    }
    
    /// Sets the comment text for a move at the given index
    func setMoveCommentText(_ text: String, at moveIndex: MoveTree.MoveIndex) {
        annotationManager.setMoveCommentText(text, at: moveIndex, in: session)
    }

    /// Gets the current comment text at the given index
    func getMoveCommentText(at moveIndex: MoveTree.MoveIndex) -> String {
        return annotationManager.getMoveCommentText(at: moveIndex, in: session)
    }
    
    // MARK: - Visual Annotations Editing (Delegated to AnnotationManager)

    /// Sets the annotation color
    func setAnnotationColor(_ color: Move.VisualAnnotations.AnnotationColor) {
        annotationManager.setAnnotationColor(color, in: session)
    }

    /// Toggles the board orientation
    func toggleBoardFlip() {
        annotationManager.toggleBoardFlip(in: session)
    }
    
    /// Toggles a square highlight for the current move position
    func toggleSquareHighlight(at square: Square) {
        annotationManager.toggleSquareHighlight(at: square, in: session)
    }
    
    /// Toggles an arrow annotation for the current move position
    func toggleArrow(from: Square, to: Square) {
        annotationManager.toggleArrow(from: from, to: to, in: session)
    }
}

// MARK: - Metadata Binding Extensions
extension ChessGameViewModel {
    
    /// Creates a binding for any metadata field using KeyPath
    func metadataBinding<T>(_ keyPath: WritableKeyPath<Game.Metadata, T>) -> Binding<T> {
        Binding(
            get: { self.session.game.metadata[keyPath: keyPath] },
            set: {
                self.session.game.metadata[keyPath: keyPath] = $0
                self.session.isModified = true
                // Trigger UI update when metadata changes
                self.objectWillChange.send()
            }
        )
    }
    
    /// Convenience bindings for common metadata fields
    var eventBinding: Binding<String> { metadataBinding(\.event) }
    var siteBinding: Binding<String> { metadataBinding(\.site) }
    var dateBinding: Binding<String> { metadataBinding(\.date) }
    var roundBinding: Binding<String> { metadataBinding(\.round) }
    var whiteBinding: Binding<String> { metadataBinding(\.white) }
    var blackBinding: Binding<String> { metadataBinding(\.black) }
    var whiteTeamBinding: Binding<String> { metadataBinding(\.whiteTeam) }
    var blackTeamBinding: Binding<String> { metadataBinding(\.blackTeam) }
    var whiteTitleBinding: Binding<String> { metadataBinding(\.whiteTitle) }
    var blackTitleBinding: Binding<String> { metadataBinding(\.blackTitle) }
    var whiteEloBinding: Binding<String> { metadataBinding(\.whiteElo) }
    var blackEloBinding: Binding<String> { metadataBinding(\.blackElo) }
    var whiteFideIdBinding: Binding<String> { metadataBinding(\.whiteFideId) }
    var blackFideIdBinding: Binding<String> { metadataBinding(\.blackFideId) }
    var ecoBinding: Binding<String> { metadataBinding(\.eco) }
    var openingBinding: Binding<String> { metadataBinding(\.opening) }
    var timeControlBinding: Binding<String> { metadataBinding(\.timeControl) }
    var notesBinding: Binding<String> { metadataBinding(\.notes) }
    var setUpBinding: Binding<String> { metadataBinding(\.setUp) }
    var fenBinding: Binding<String> { metadataBinding(\.fen) }
    
    /// Special binding for result field with default value handling
    var resultBinding: Binding<String> {
        Binding(
            get: {
                let result = self.session.game.metadata.result
                return result.isEmpty ? "*" : result
            },
            set: {
                self.session.game.metadata.result = $0
                self.session.isModified = true
                self.objectWillChange.send()
            }
        )
    }
    
    /// Auto-detects game result based on current game status
    func autoDetectGameResult() {
        
        // Only update if result is currently unset
        if session.game.metadata.result.isEmpty || session.game.metadata.result == "*" {
            switch gameStatus {
            case .checkmate(let color):
                session.game.metadata.result = color == .white ? "1-0" : "0-1"
            case .stalemate, .draw:
                session.game.metadata.result = "1/2-1/2"
            case .inProgress:
                session.game.metadata.result = "*"
            }
        }
    }
}

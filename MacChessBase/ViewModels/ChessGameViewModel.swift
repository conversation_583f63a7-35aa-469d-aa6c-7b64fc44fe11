//
//  ChessGameViewModel.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit
import Combine

/// Simplified ViewModel that acts as a pure UI adapter using event-driven architecture
@MainActor
final class ChessGameViewModel: ObservableObject {
    
    // MARK: - Dependencies
    private let stateManager = ChessStateManager.shared
    private let eventBus = ChessEventBus.shared
    private let subscriptionManager = EventSubscriptionManager()
    
    // MARK: - Services
    private let moveExecutor = ChessMoveExecutor()
    private let navigator = ChessNavigator()
    let dragManager = ChessDragManager() // Public for ChessBoardView
    private let fileOperations = ChessFileOperations()
    private let annotationManager = ChessAnnotationManager()
    private let moveDisplayManager = MoveDisplayManager()
    
    // MARK: - Session Reference
    @ObservedObject var session: GameSession
    
    // MARK: - Initialization
    init(session: GameSession) {
        self.session = session
        
        // Set the current session in state manager
        stateManager.setCurrentSession(session)
        
        // Setup move display manager
        moveDisplayManager.updateSession(session)
        
        // Setup event subscriptions
        setupEventSubscriptions()
        
        print("🎯 ChessGameViewModel initialized with session")
    }
    
    // MARK: - Event Subscriptions
    private func setupEventSubscriptions() {
        // Subscribe to state changes to trigger UI updates
        subscriptionManager.subscribe(to: "*") { [weak self] _ in
            self?.objectWillChange.send()
        }
    }
    
    // MARK: - UI State Access (Delegated to StateManager)
    
    var selectedSquare: Square? {
        get { stateManager.selectedSquare }
        set { 
            if let square = newValue {
                dragManager.selectSquare(square, in: session)
            } else {
                dragManager.clearSelection()
            }
        }
    }
    
    var possibleMoves: [Square] {
        stateManager.possibleMoves
    }
    
    var lastMove: Move? {
        stateManager.lastMove
    }
    
    var isReverseDragActive: Bool {
        stateManager.isReverseDragActive
    }
    
    var reverseDragTarget: Square? {
        stateManager.reverseDragTarget
    }
    
    var reverseDragValidSources: [Square] {
        stateManager.reverseDragValidSources
    }
    
    var showPromotionDialog: Bool {
        get { stateManager.showPromotionDialog }
        set { /* Handled by events */ }
    }
    
    var showVariationCreationDialog: Bool {
        get { stateManager.showVariationCreationDialog }
        set { /* Handled by events */ }
    }
    
    var showVariationSelection: Bool {
        get { stateManager.showVariationSelection }
        set { /* Handled by events */ }
    }
    
    var promotionMove: Move? {
        stateManager.promotionMove
    }
    
    var pendingMove: Move? {
        stateManager.pendingMove
    }
    
    var pendingMoveFromIndex: MoveTree.MoveIndex? {
        stateManager.pendingMoveFromIndex
    }
    
    var existingNextMoveIndex: MoveTree.MoveIndex? {
        stateManager.existingNextMoveIndex
    }
    
    var availableVariations: [VariationOption] {
        stateManager.availableVariations
    }
    
    var isKeyboardNavigationDisabled: Bool {
        stateManager.isKeyboardNavigationDisabled
    }
    
    var gameStatus: GameStatus {
        stateManager.gameStatus
    }
    
    var isBoardFlipped: Bool {
        get { stateManager.isBoardFlipped }
        set { stateManager.setBoardFlipped(newValue) }
    }
    
    var currentAnnotationColor: Move.VisualAnnotations.AnnotationColor {
        get { stateManager.currentAnnotationColor }
        set { stateManager.setAnnotationColor(newValue) }
    }
    
    // MARK: - Engine State Access
    var isEngineRunning: Bool { stateManager.isEngineRunning }
    var isEngineAnalyzing: Bool { stateManager.isEngineAnalyzing }
    var isEnginePaused: Bool { stateManager.isEnginePaused }
    var engineState: EngineState { stateManager.engineState }
    var currentEngineEvaluation: EngineEvaluation? { stateManager.currentEngineEvaluation }
    var engineLines: [EngineLine] { stateManager.engineLines }
    
    // MARK: - Game State Access
    var canGoToPreviousMove: Bool { stateManager.canGoToPreviousMove }
    var canGoToNextMove: Bool { stateManager.canGoToNextMove }
    var currentPlayer: Piece.Color { stateManager.currentPlayer }
    var pgn: String { stateManager.pgn }
    
    // MARK: - Move Operations (Delegated to Services)
    
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        let result = moveExecutor.attemptMove(from: startSquare, to: endSquare, in: session)
        
        switch result {
        case .success(_):
            // Move completed successfully, events will handle UI updates
            break
            
        case .invalidMove, .shouldDeselect:
            stateManager.clearAllSelections()
            
        case .shouldReselect(let square):
            selectedSquare = square
            
        case .variationDialogNeeded:
            // Dialog will be shown via events
            break
        }
    }
    
    func selectSquare(_ square: Square) {
        dragManager.selectSquare(square, in: session)
    }
    
    func clearSelection() {
        dragManager.clearSelection()
    }
    
    func clearAllSelections() {
        stateManager.clearAllSelections()
    }
    
    // MARK: - Navigation Operations (Delegated to Navigator)
    
    func goToPreviousMove() {
        navigator.goToPreviousMove(in: session)
    }
    
    func goToNextMove() {
        navigator.goToNextMove(in: session)
    }
    
    func goToStart() {
        navigator.goToStart(in: session)
    }
    
    func goToEnd() {
        navigator.goToEnd(in: session)
    }
    
    func goToMove(at index: MoveTree.MoveIndex) {
        navigator.goToMove(at: index, in: session)
    }
    
    func selectVariation(_ option: VariationOption) {
        navigator.selectVariation(option, in: session)
    }
    
    func cancelVariationSelection() {
        navigator.cancelVariationSelection()
    }
    
    // MARK: - Promotion Operations
    
    func selectPromotionPiece(_ pieceKind: Piece.Kind) {
        guard let move = promotionMove else { return }
        
        Task { @MainActor in
            _ = moveExecutor.completePromotion(move: move, to: pieceKind, in: session)
            eventBus.publish(SystemEvent.stateReset) // Clear promotion dialog
        }
    }
    
    func cancelPromotion() {
        eventBus.publish(SystemEvent.stateReset) // Clear promotion dialog
    }
    
    // MARK: - Variation Creation Operations
    
    func selectVariationCreationOption(_ option: VariationCreationOption) {
        guard let move = pendingMove else { return }
        let fromIndex = pendingMoveFromIndex ?? session.currentMoveIndex
        
        Task { @MainActor in
            _ = moveExecutor.executeMove(move, option: option, from: fromIndex, in: session)
            eventBus.publish(SystemEvent.stateReset) // Clear variation dialog
        }
    }
    
    func cancelVariationCreation() {
        eventBus.publish(SystemEvent.stateReset) // Clear variation dialog
    }
    
    // MARK: - Game Management
    
    func newGame() {
        session.newGame()
        eventBus.publish(GameEvent.gameReset)
    }
    
    // MARK: - File Operations (Delegated to FileOperations)
    
    func loadGame(from pgn: String) {
        fileOperations.loadGame(from: pgn, into: session)
    }
    
    func saveGame() {
        try? fileOperations.saveGame(from: session)
    }

    func saveGameAs() {
        // Implementation for save as dialog
        // This would need to be implemented in ChessFileOperations
    }

    func exportPGN() -> String {
        return session.pgn
    }
    
    // MARK: - Engine Operations
    
    func getEngineManager() -> EngineManager {
        return EngineManager.shared
    }
    
    // MARK: - Drag Operations (Delegated to DragManager)
    
    func validateDragStart(piece: Piece, from square: Square) -> Bool {
        return dragManager.validateDragStart(piece: piece, from: square, in: session)
    }
    
    func startReverseDrag(at targetSquare: Square) -> Bool {
        return dragManager.startReverseDrag(from: targetSquare, in: session)
    }
    
    func cancelReverseDrag() {
        dragManager.cancelReverseDrag()
    }
    
    func canMovePiece(at square: Square) -> Bool {
        return dragManager.canMovePiece(at: square, in: session)
    }

    // MARK: - Annotation Operations (Delegated to AnnotationManager)

    func addVisualAnnotation(_ annotation: Move.VisualAnnotations) {
        // Implementation would depend on ChessAnnotationManager API
        eventBus.publish(UIEvent.annotationAdded(annotation))
    }

    func removeVisualAnnotation(_ annotation: Move.VisualAnnotations) {
        // Implementation would depend on ChessAnnotationManager API
        eventBus.publish(UIEvent.annotationRemoved(annotation))
    }

    func toggleSquareHighlight(at square: Square) {
        annotationManager.toggleSquareHighlight(at: square, in: session)
    }

    func toggleArrow(from: Square, to: Square) {
        annotationManager.toggleArrow(from: from, to: to, in: session)
    }

    func clearAllAnnotations() {
        // Implementation would depend on ChessAnnotationManager API
    }

    // MARK: - Move Display Operations (Delegated to MoveDisplayManager)

    var moveDisplayText: String {
        // Implementation would depend on MoveDisplayManager API
        return ""
    }

    var currentMoveDisplayIndex: Int? {
        // Implementation would depend on MoveDisplayManager API
        return nil
    }

    func invalidateCache() {
        moveDisplayManager.invalidateCache()
        eventBus.publish(SystemEvent.cacheInvalidated)
    }

    // MARK: - Metadata Operations

    func createBinding<T>(for keyPath: WritableKeyPath<Game.Metadata, T>) -> Binding<T> {
        Binding(
            get: { self.session.game.metadata[keyPath: keyPath] },
            set: { newValue in
                self.session.game.metadata[keyPath: keyPath] = newValue
                self.session.isModified = true
                self.eventBus.publish(GameEvent.gameModified)
            }
        )
    }

    var resultBinding: Binding<String> {
        Binding(
            get: {
                let result = self.session.game.metadata.result
                return result.isEmpty ? "*" : result
            },
            set: {
                self.session.game.metadata.result = $0
                self.session.isModified = true
                self.eventBus.publish(GameEvent.gameModified)
            }
        )
    }

    func autoDetectGameResult() {
        if session.game.metadata.result.isEmpty || session.game.metadata.result == "*" {
            switch gameStatus {
            case .checkmate(let color):
                session.game.metadata.result = color == .white ? "1-0" : "0-1"
            case .stalemate, .draw:
                session.game.metadata.result = "1/2-1/2"
            case .inProgress:
                session.game.metadata.result = "*"
            }
            eventBus.publish(GameEvent.gameModified)
        }
    }

    // MARK: - Position Editor Operations

    func openPositionEditor() {
        // This would trigger opening the position editor
        // Implementation depends on how the position editor is integrated
    }

    func setPosition(_ position: Position, shouldFlip: Bool = false) -> Bool {
        let success = session.setPosition(position, shouldFlip: shouldFlip)
        if success {
            eventBus.publish(GameEvent.positionChanged(position))
            if shouldFlip {
                eventBus.publish(UIEvent.boardFlipped(true))
            }
        }
        return success
    }

    // MARK: - Utility Methods

    private func checkGameStatus(_ move: Move) {
        let newStatus = session.gameStatus
        if newStatus != gameStatus {
            eventBus.publish(GameEvent.gameStatusChanged(newStatus))
        }
    }

    // MARK: - Missing Methods from Original ViewModel

    func handleSquarePress(_ square: Square) {
        if let selectedSquare = selectedSquare {
            attemptMove(from: selectedSquare, to: square)
        } else {
            selectSquare(square)
        }
    }

    func completePromotion(to pieceKind: Piece.Kind) {
        guard let move = promotionMove else { return }
        _ = moveExecutor.completePromotion(move: move, to: pieceKind, in: session)
        eventBus.publish(SystemEvent.stateReset)
    }

    func setSelectedSquare(_ square: Square) {
        dragManager.setSelectedSquare(square, in: session)
    }

    func loadGame(from url: URL) {
        fileOperations.loadGame(from: url, into: session)
    }

    func loadGameFromObject(_ gameObject: Game) {
        fileOperations.loadGameFromObject(gameObject, into: session)
    }

    func loadPosition(from fen: String) {
        fileOperations.loadPosition(from: fen, into: session)
    }

    func saveGame(to url: URL) throws {
        try fileOperations.saveGame(to: url, from: session)
    }

    func loadGameFromClipboard() {
        if !fileOperations.loadGameFromClipboard(into: session) {
            // Handle error
        }
    }

    func showEditMenu(for moveIndex: MoveTree.MoveIndex, at position: CGPoint) {
        // Implementation for showing edit menu
    }

    func hideEditMenu() {
        // Implementation for hiding edit menu
    }

    func deleteMove(at index: MoveTree.MoveIndex) {
        guard session.deleteMove(at: index) == true else { return }
        eventBus.publish(GameEvent.gameModified)
    }

    func deleteVariation(at index: MoveTree.MoveIndex) {
        if let rootNodeIndex = session.game.moves.getVariationRootNodeIndex(index: index) {
            _ = session.deleteMove(at: rootNodeIndex)
            eventBus.publish(GameEvent.gameModified)
        }
    }

    func deleteBeforeMove(at index: MoveTree.MoveIndex) {
        guard session.deleteBeforeMove(at: index) == true else { return }
        eventBus.publish(GameEvent.gameModified)
    }

    func promoteVariation(at index: MoveTree.MoveIndex) {
        guard session.promoteVariation(at: index) == true else { return }
        eventBus.publish(GameEvent.gameModified)
    }

    func promoteToMainVariation(at index: MoveTree.MoveIndex) {
        guard session.promoteToMainVariation(at: index) == true else { return }
        eventBus.publish(GameEvent.gameModified)
    }

    func canPromoteVariation(at index: MoveTree.MoveIndex) -> Bool {
        return !session.game.moves.isOnMainVariation(index: index)
    }

    func startReverseDrag(from targetSquare: Square) -> Bool {
        return dragManager.startReverseDrag(from: targetSquare, in: session)
    }

    func completeReverseDrag(to sourceSquare: Square) {
        // Implementation for completing reverse drag
        if let target = stateManager.reverseDragTarget {
            attemptMove(from: sourceSquare, to: target)
        }
        cancelReverseDrag()
    }

    func formatPlayerInfo(name: String, color: Piece.Color) -> String {
        if name.isEmpty {
            return color == .white ? "White" : "Black"
        }
        return name
    }

    func setMoveAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex) {
        annotationManager.setMoveAssessment(assessment, at: moveIndex, in: session)
        eventBus.publish(GameEvent.gameModified)
    }

    func setPositionAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex) {
        annotationManager.setPositionAssessment(assessment, at: moveIndex, in: session)
        eventBus.publish(GameEvent.gameModified)
    }

    func setMoveCommentText(_ text: String, at moveIndex: MoveTree.MoveIndex) {
        annotationManager.setMoveCommentText(text, at: moveIndex, in: session)
        eventBus.publish(GameEvent.gameModified)
    }

    func getMoveCommentText(at moveIndex: MoveTree.MoveIndex) -> String {
        return annotationManager.getMoveCommentText(at: moveIndex, in: session)
    }

    func setAnnotationColor(_ color: Move.VisualAnnotations.AnnotationColor) {
        stateManager.setAnnotationColor(color)
    }

    func toggleBoardFlip() {
        stateManager.setBoardFlipped(!stateManager.isBoardFlipped)
    }

    func metadataBinding<T>(_ keyPath: WritableKeyPath<Game.Metadata, T>) -> Binding<T> {
        return createBinding(for: keyPath)
    }

    // MARK: - Specific Metadata Bindings

    var eventBinding: Binding<String> {
        createBinding(for: \.event)
    }

    var siteBinding: Binding<String> {
        createBinding(for: \.site)
    }

    var dateBinding: Binding<String> {
        createBinding(for: \.date)
    }

    var roundBinding: Binding<String> {
        createBinding(for: \.round)
    }

    var whiteBinding: Binding<String> {
        createBinding(for: \.white)
    }

    var blackBinding: Binding<String> {
        createBinding(for: \.black)
    }

    var whiteTeamBinding: Binding<String> {
        createBinding(for: \.whiteTeam)
    }

    var blackTeamBinding: Binding<String> {
        createBinding(for: \.blackTeam)
    }

    var whiteTitleBinding: Binding<String> {
        createBinding(for: \.whiteTitle)
    }

    var blackTitleBinding: Binding<String> {
        createBinding(for: \.blackTitle)
    }

    var whiteEloBinding: Binding<String> {
        createBinding(for: \.whiteElo)
    }

    var blackEloBinding: Binding<String> {
        createBinding(for: \.blackElo)
    }

    var whiteFideIdBinding: Binding<String> {
        createBinding(for: \.whiteFideId)
    }

    var blackFideIdBinding: Binding<String> {
        createBinding(for: \.blackFideId)
    }

    var ecoBinding: Binding<String> {
        createBinding(for: \.eco)
    }

    var openingBinding: Binding<String> {
        createBinding(for: \.opening)
    }

    var timeControlBinding: Binding<String> {
        createBinding(for: \.timeControl)
    }

    var notesBinding: Binding<String> {
        createBinding(for: \.notes)
    }

    var fenBinding: Binding<String> {
        createBinding(for: \.fen)
    }

    var setUpBinding: Binding<String> {
        createBinding(for: \.setUp)
    }

    // MARK: - Move Display Properties

    var cachedMoves: [MoveDisplayItem] {
        // Implementation would depend on MoveDisplayManager API
        return []
    }

    var hasOnlyCurrentMoveChanged: Bool {
        // Implementation would depend on MoveDisplayManager API
        return false
    }

    // MARK: - Additional UI State Properties

    var showPositionEditor: Bool {
        get { false } // Implementation needed
        set { /* Implementation needed */ }
    }

    var showImportErrorAlert: Bool {
        get { false } // Implementation needed
        set { /* Implementation needed */ }
    }
}

//
//  ChessNavigator.swift
//  MacChessBase
//
//  Created by AI on 2025/8/9.
//

import SwiftUI
import Chess<PERSON>it

/// Represents a variation option for user selection
struct VariationOption: Identifiable {
    let id: Int
    let index: MoveTree.MoveIndex
    let pgnText: String
    let isMainLine: Bool
}

/// Handles chess game navigation logic using event-driven architecture
@MainActor
final class ChessNavigator: ObservableObject {

    // MARK: - Event Bus
    private let eventBus = ChessEventBus.shared
    private let subscriptionManager = EventSubscriptionManager()

    // MARK: - Initialization
    init() {
        setupEventSubscriptions()
    }

    // MARK: - Event Subscriptions
    private func setupEventSubscriptions() {
        // Listen for navigation events
        subscriptionManager.subscribe(to: MoveEvent.self) { [weak self] event in
            switch event {
            case .navigationRequested(let type):
                // Handle navigation requests if needed
                break
            case .variationSelected(let index):
                // Handle variation selection
                break
            default:
                break
            }
        }
    }
    
    // MARK: - Public Navigation Interface
    
    /// Goes to the previous move in the game
    func goToPreviousMove(in session: GameSession) {
        session.goToPreviousMove()
        eventBus.publish(MoveEvent.navigationRequested(.previous))
    }

    /// Goes to the next move in the game
    func goToNextMove(in session: GameSession) {
        guard canGoToNextMove(in: session) else { return }

        // Get available next moves from session
        let nextMoves = session.goToNextMove()

        guard !nextMoves.isEmpty else { return }

        if nextMoves.count == 1 {
            // Only one option, go directly
            session.goToMove(at: nextMoves[0])
            eventBus.publish(MoveEvent.navigationRequested(.next))
        } else {
            // Multiple options, show selection dialog
            let mainLineIndex = nextMoves[0]
            let variationIndices = Array(nextMoves.dropFirst())
            showVariationSelectionDialog(mainLineIndex: mainLineIndex,
                                       variationIndices: variationIndices,
                                       in: session)
        }
    }

    /// Goes to the start of the game
    func goToStart(in session: GameSession) {
        session.goToStart()
        eventBus.publish(MoveEvent.navigationRequested(.start))
    }

    /// Goes to the end of the game (last move played)
    func goToEnd(in session: GameSession) {
        session.goToEnd()
        eventBus.publish(MoveEvent.navigationRequested(.end))
    }

    /// Goes to a specific move index
    func goToMove(at index: MoveTree.MoveIndex, in session: GameSession) {
        session.goToMove(at: index)
        eventBus.publish(MoveEvent.navigationRequested(.toIndex(index)))
    }
    
    // MARK: - Navigation State Queries
    
    /// Checks if we can go to the previous move
    func canGoToPreviousMove(in session: GameSession) -> Bool {
        return session.canGoToPreviousMove
    }
    
    /// Checks if we can go to the next move
    func canGoToNextMove(in session: GameSession) -> Bool {
        return session.canGoToNextMove
    }
    
    // MARK: - Variation Selection Interface
    
    /// Selects a variation and navigates to it
    func selectVariation(_ option: VariationOption, in session: GameSession) {
        Task { @MainActor in
            // Navigate to the selected variation through the session
            session.goToMove(at: option.index)
            eventBus.publish(MoveEvent.variationSelected(option.index))
        }
    }

    /// Cancels variation selection
    func cancelVariationSelection() {
        Task { @MainActor in
            // Publish event to clear variation selection UI
            eventBus.publish(UIEvent.squareDeselected)
        }
    }
    
    // MARK: - Private Implementation
    
    /// Shows the variation selection dialog
    private func showVariationSelectionDialog(mainLineIndex: MoveTree.MoveIndex?,
                                            variationIndices: [MoveTree.MoveIndex],
                                            in session: GameSession) {
        var options: [VariationOption] = []

        // Add main line option if available
        if let mainLineIndex = mainLineIndex {
            let mainLinePGN = generatePGNTextForMove(at: mainLineIndex, in: session)
            options.append(VariationOption(
                id: 0,
                index: mainLineIndex,
                pgnText: mainLinePGN,
                isMainLine: true
            ))
        }

        // Add variation options
        for (i, variationIndex) in variationIndices.enumerated() {
            let variationPGN = generatePGNTextForMove(at: variationIndex, in: session)
            options.append(VariationOption(
                id: i+1,
                index: variationIndex,
                pgnText: variationPGN,
                isMainLine: false
            ))
        }

        // Sort by main line first, then by move hash for consistent ordering
        options.sort(by: { lhs, rhs in
            lhs.id < rhs.id
        })

        // Publish event to show variation selection with options
        eventBus.publish(UIEvent.squareSelected(.a1)) // Placeholder event
    }
    
    /// Generates PGN text for a move and its continuation
    private func generatePGNTextForMove(at index: MoveTree.MoveIndex, in session: GameSession) -> String {
        // Use the variationPGN method to get the proper PGN representation
        let pgnElements = session.game.moves.variationPGN(from: index)
        
        var result = ""
        var moveCount = 0
        let maxMovesToShow = 6 // Show up to 3 move pairs
        
        for element in pgnElements {
            if moveCount >= maxMovesToShow { break }
            
            switch element {
            case .whiteNumber(let number):
                if !result.isEmpty { result += " " }
                result += "\(number)."
                
            case .blackNumber(let number):
                if moveCount != 0 {
                    break
                }
                if !result.isEmpty { result += " " }
                result += "\(number)…"
                
            case .move(let move, _):
                if !result.isEmpty && !result.hasSuffix(".") && !result.hasSuffix("…") {
                    result += " "
                }
                result += move.metaMove!.displayDescription
                moveCount += 1
                
            case .variationStart, .variationEnd:
                // Skip variation markers for this display
                break
            }
        }
        
        return result
    }
}
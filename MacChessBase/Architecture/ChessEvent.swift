//
//  ChessEvent.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import ChessKit

/// Base protocol for all chess events
protocol ChessEvent {
    /// Unique identifier for the event type
    var eventType: String { get }
    /// Timestamp when the event was created
    var timestamp: Date { get }
}

/// Extension to provide default implementations
extension ChessEvent {
    var timestamp: Date { Date() }
}

// MARK: - Game Events

/// Events related to game state changes
enum GameEvent: ChessEvent {
    case gameStarted(Game)
    case gameLoaded(Game)
    case gameReset
    case positionChanged(Position)
    case moveIndexChanged(MoveTree.MoveIndex)
    case gameStatusChanged(GameStatus)
    case gameModified
    
    var eventType: String {
        switch self {
        case .gameStarted: return "game.started"
        case .gameLoaded: return "game.loaded"
        case .gameReset: return "game.reset"
        case .positionChanged: return "game.position.changed"
        case .moveIndexChanged: return "game.moveIndex.changed"
        case .gameStatusChanged: return "game.status.changed"
        case .gameModified: return "game.modified"
        }
    }
}

// MARK: - Move Events

/// Events related to move execution and navigation
enum MoveEvent: ChessEvent {
    case moveAttempted(from: Square, to: Square)
    case moveExecuted(Move)
    case moveUndone(Move)
    case promotionRequired(Move)
    case variationCreationRequired(move: Move, fromIndex: MoveTree.MoveIndex, existingIndex: MoveTree.MoveIndex?)
    case navigationRequested(NavigationType)
    case variationSelected(MoveTree.MoveIndex)
    
    var eventType: String {
        switch self {
        case .moveAttempted: return "move.attempted"
        case .moveExecuted: return "move.executed"
        case .moveUndone: return "move.undone"
        case .promotionRequired: return "move.promotion.required"
        case .variationCreationRequired: return "move.variation.required"
        case .navigationRequested: return "move.navigation.requested"
        case .variationSelected: return "move.variation.selected"
        }
    }
}

/// Navigation types for move events
enum NavigationType {
    case previous
    case next
    case start
    case end
    case toIndex(MoveTree.MoveIndex)
}

// MARK: - UI Events

/// Events related to user interface interactions
enum UIEvent: ChessEvent {
    case squareSelected(Square)
    case squareDeselected
    case boardFlipped(Bool)
    case dragStarted(from: Square, piece: Piece)
    case dragEnded(from: Square, to: Square)
    case reverseDragStarted(target: Square)
    case reverseDragEnded
    case annotationAdded(Move.VisualAnnotations)
    case annotationRemoved(Move.VisualAnnotations)
    
    var eventType: String {
        switch self {
        case .squareSelected: return "ui.square.selected"
        case .squareDeselected: return "ui.square.deselected"
        case .boardFlipped: return "ui.board.flipped"
        case .dragStarted: return "ui.drag.started"
        case .dragEnded: return "ui.drag.ended"
        case .reverseDragStarted: return "ui.reverseDrag.started"
        case .reverseDragEnded: return "ui.reverseDrag.ended"
        case .annotationAdded: return "ui.annotation.added"
        case .annotationRemoved: return "ui.annotation.removed"
        }
    }
}

// MARK: - Engine Events

/// Events related to chess engine operations
enum EngineEvent: ChessEvent {
    case engineStarted
    case engineStopped
    case analysisStarted(Position)
    case analysisUpdated(EngineEvaluation, [EngineLine])
    case analysisPaused
    case analysisResumed
    case engineError(String)
    
    var eventType: String {
        switch self {
        case .engineStarted: return "engine.started"
        case .engineStopped: return "engine.stopped"
        case .analysisStarted: return "engine.analysis.started"
        case .analysisUpdated: return "engine.analysis.updated"
        case .analysisPaused: return "engine.analysis.paused"
        case .analysisResumed: return "engine.analysis.resumed"
        case .engineError: return "engine.error"
        }
    }
}

// MARK: - File Events

/// Events related to file operations
enum FileEvent: ChessEvent {
    case fileLoaded(URL)
    case fileSaved(URL)
    case fileImported(String) // PGN content
    case fileExported(String) // PGN content
    case fileError(String)
    
    var eventType: String {
        switch self {
        case .fileLoaded: return "file.loaded"
        case .fileSaved: return "file.saved"
        case .fileImported: return "file.imported"
        case .fileExported: return "file.exported"
        case .fileError: return "file.error"
        }
    }
}

// MARK: - Session Events

/// Events related to session management
enum SessionEvent: ChessEvent {
    case sessionCreated(GameSession)
    case sessionActivated(GameSession)
    case sessionDeactivated(GameSession)
    case sessionRemoved(UUID)
    case sessionRenamed(UUID, String)
    
    var eventType: String {
        switch self {
        case .sessionCreated: return "session.created"
        case .sessionActivated: return "session.activated"
        case .sessionDeactivated: return "session.deactivated"
        case .sessionRemoved: return "session.removed"
        case .sessionRenamed: return "session.renamed"
        }
    }
}

// MARK: - System Events

/// Events related to system-level operations
enum SystemEvent: ChessEvent {
    case cacheInvalidated
    case stateReset
    case errorOccurred(String)
    case debugMessage(String)
    
    var eventType: String {
        switch self {
        case .cacheInvalidated: return "system.cache.invalidated"
        case .stateReset: return "system.state.reset"
        case .errorOccurred: return "system.error"
        case .debugMessage: return "system.debug"
        }
    }
}

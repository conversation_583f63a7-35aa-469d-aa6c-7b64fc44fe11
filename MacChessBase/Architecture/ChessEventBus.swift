//
//  ChessEventBus.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import Combine

/// Type alias for event handlers
typealias EventHandler = (ChessEvent) -> Void

/// Central event bus for coordinating communication between chess components
@MainActor
final class ChessEventBus: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ChessEventBus()
    
    // MARK: - Private Properties
    private var eventHandlers: [String: [UUID: EventHandler]] = [:]
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Published Properties for UI Binding
    @Published private(set) var lastEvent: ChessEvent?
    @Published private(set) var eventCount: Int = 0
    
    // MARK: - Event Stream
    private let eventSubject = PassthroughSubject<ChessEvent, Never>()
    
    /// Publisher for observing all events
    var eventPublisher: AnyPublisher<ChessEvent, Never> {
        eventSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    private init() {
        setupEventLogging()
    }
    
    // MARK: - Event Publishing
    
    /// Publishes an event to all registered handlers
    /// - Parameter event: The event to publish
    func publish(_ event: ChessEvent) {
        #if DEBUG
        print("🚀 EventBus: Publishing \(event.eventType)")
        #endif
        
        // Update published properties
        lastEvent = event
        eventCount += 1
        
        // Send to Combine stream
        eventSubject.send(event)
        
        // Send to registered handlers
        let eventType = event.eventType
        if let handlers = eventHandlers[eventType] {
            for handler in handlers.values {
                handler(event)
            }
        }
        
        // Also send to wildcard handlers (listening to all events)
        if let wildcardHandlers = eventHandlers["*"] {
            for handler in wildcardHandlers.values {
                handler(event)
            }
        }
    }
    
    // MARK: - Event Subscription
    
    /// Subscribes to events of a specific type
    /// - Parameters:
    ///   - eventType: The type of events to listen for (use "*" for all events)
    ///   - handler: The handler function to call when events occur
    /// - Returns: A subscription ID that can be used to unsubscribe
    @discardableResult
    func subscribe(to eventType: String, handler: @escaping EventHandler) -> UUID {
        let subscriptionId = UUID()
        
        if eventHandlers[eventType] == nil {
            eventHandlers[eventType] = [:]
        }
        eventHandlers[eventType]?[subscriptionId] = handler
        
        #if DEBUG
        print("📡 EventBus: Subscribed to \(eventType) with ID \(subscriptionId)")
        #endif
        
        return subscriptionId
    }
    
    /// Subscribes to events using a generic event type
    /// - Parameters:
    ///   - eventType: The event enum type to listen for
    ///   - handler: The handler function with typed event parameter
    /// - Returns: A subscription ID that can be used to unsubscribe
    @discardableResult
    func subscribe<T: ChessEvent>(to eventType: T.Type, handler: @escaping (T) -> Void) -> UUID {
        return subscribe(to: "*") { event in
            if let typedEvent = event as? T {
                handler(typedEvent)
            }
        }
    }
    
    /// Unsubscribes from events using the subscription ID
    /// - Parameter subscriptionId: The ID returned from subscribe
    func unsubscribe(_ subscriptionId: UUID) {
        for (eventType, handlers) in eventHandlers {
            if handlers[subscriptionId] != nil {
                eventHandlers[eventType]?.removeValue(forKey: subscriptionId)
                #if DEBUG
                print("📡 EventBus: Unsubscribed \(subscriptionId) from \(eventType)")
                #endif
                break
            }
        }
    }
    
    /// Unsubscribes all handlers for a specific event type
    /// - Parameter eventType: The event type to clear
    func unsubscribeAll(from eventType: String) {
        eventHandlers[eventType] = nil
        #if DEBUG
        print("📡 EventBus: Unsubscribed all handlers from \(eventType)")
        #endif
    }
    
    /// Clears all event subscriptions
    func clearAllSubscriptions() {
        eventHandlers.removeAll()
        #if DEBUG
        print("📡 EventBus: Cleared all subscriptions")
        #endif
    }
    
    // MARK: - Convenience Methods
    
    /// Publishes a game event
    func publish(_ event: GameEvent) {
        publish(event as ChessEvent)
    }
    
    /// Publishes a move event
    func publish(_ event: MoveEvent) {
        publish(event as ChessEvent)
    }
    
    /// Publishes a UI event
    func publish(_ event: UIEvent) {
        publish(event as ChessEvent)
    }
    
    /// Publishes an engine event
    func publish(_ event: EngineEvent) {
        publish(event as ChessEvent)
    }
    
    /// Publishes a file event
    func publish(_ event: FileEvent) {
        publish(event as ChessEvent)
    }
    
    /// Publishes a session event
    func publish(_ event: SessionEvent) {
        publish(event as ChessEvent)
    }
    
    /// Publishes a system event
    func publish(_ event: SystemEvent) {
        publish(event as ChessEvent)
    }
    
    // MARK: - Debugging and Monitoring
    
    /// Sets up event logging for debugging
    private func setupEventLogging() {
        #if DEBUG
        subscribe(to: "*") { event in
            print("📋 EventBus Log: \(event.eventType) at \(event.timestamp)")
        }
        #endif
    }
    
    /// Gets statistics about current subscriptions
    var subscriptionStats: [String: Int] {
        var stats: [String: Int] = [:]
        for (eventType, handlers) in eventHandlers {
            stats[eventType] = handlers.count
        }
        return stats
    }
    
    /// Gets the total number of active subscriptions
    var totalSubscriptions: Int {
        eventHandlers.values.reduce(0) { $0 + $1.count }
    }
}

// MARK: - Subscription Management Helper

/// Helper class for managing event subscriptions with automatic cleanup
@MainActor
final class EventSubscriptionManager {
    private var subscriptionIds: [UUID] = []
    private let eventBus: ChessEventBus

    init(eventBus: ChessEventBus? = nil) {
        self.eventBus = eventBus ?? ChessEventBus.shared
    }

    /// Subscribes to an event and tracks the subscription for automatic cleanup
    @discardableResult
    func subscribe(to eventType: String, handler: @escaping EventHandler) -> UUID {
        let id = eventBus.subscribe(to: eventType, handler: handler)
        subscriptionIds.append(id)
        return id
    }

    /// Subscribes to a typed event and tracks the subscription for automatic cleanup
    @discardableResult
    func subscribe<T: ChessEvent>(to eventType: T.Type, handler: @escaping (T) -> Void) -> UUID {
        let id = eventBus.subscribe(to: eventType, handler: handler)
        subscriptionIds.append(id)
        return id
    }

    /// Unsubscribes all tracked subscriptions
    func unsubscribeAll() {
        for id in subscriptionIds {
            eventBus.unsubscribe(id)
        }
        subscriptionIds.removeAll()
    }

    deinit {
        // Note: We can't call async methods in deinit
        // The subscriptions will be cleaned up when the event bus is deallocated
    }
}

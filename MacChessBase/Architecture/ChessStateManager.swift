//
//  ChessStateManager.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import SwiftUI
import ChessKit
import Combine

/// Centralized state management for the chess application
@MainActor
final class ChessStateManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ChessStateManager()
    
    // MARK: - Event Bus
    private let eventBus = ChessEventBus.shared
    private let subscriptionManager = EventSubscriptionManager()
    
    // MARK: - Game State
    @Published var currentSession: GameSession?
    @Published var gameStatus: GameStatus = .inProgress
    @Published var lastMove: Move?
    
    // MARK: - UI State
    @Published var selectedSquare: Square?
    @Published var possibleMoves: [Square] = []
    @Published var isBoardFlipped: Bool = false
    
    // MARK: - Drag State
    @Published var isReverseDragActive: Bool = false
    @Published var reverseDragTarget: Square?
    @Published var reverseDragValidSources: [Square] = []
    
    // MARK: - Dialog State
    @Published var showPromotionDialog: Bool = false
    @Published var showVariationCreationDialog: Bool = false
    @Published var showVariationSelection: Bool = false
    @Published var promotionMove: Move?
    @Published var pendingMove: Move?
    @Published var pendingMoveFromIndex: MoveTree.MoveIndex?
    @Published var existingNextMoveIndex: MoveTree.MoveIndex?
    @Published var availableVariations: [VariationOption] = []
    
    // MARK: - Navigation State
    @Published var isKeyboardNavigationDisabled: Bool = false
    
    // MARK: - Engine State
    @Published var engineState: EngineState = .stopped
    @Published var currentEngineEvaluation: EngineEvaluation?
    @Published var engineLines: [EngineLine] = []
    
    // MARK: - Annotation State
    @Published var currentAnnotationColor: Move.VisualAnnotations.AnnotationColor = .green
    
    // MARK: - File State
    @Published var currentFilePath: URL?
    @Published var isModified: Bool = false
    
    // MARK: - Initialization
    private init() {
        setupEventSubscriptions()
    }
    
    // MARK: - Event Subscriptions
    private func setupEventSubscriptions() {
        // Game Events
        subscriptionManager.subscribe(to: GameEvent.self) { [weak self] event in
            self?.handleGameEvent(event)
        }
        
        // Move Events
        subscriptionManager.subscribe(to: MoveEvent.self) { [weak self] event in
            self?.handleMoveEvent(event)
        }
        
        // UI Events
        subscriptionManager.subscribe(to: UIEvent.self) { [weak self] event in
            self?.handleUIEvent(event)
        }
        
        // Engine Events
        subscriptionManager.subscribe(to: EngineEvent.self) { [weak self] event in
            self?.handleEngineEvent(event)
        }
        
        // File Events
        subscriptionManager.subscribe(to: FileEvent.self) { [weak self] event in
            self?.handleFileEvent(event)
        }
        
        // Session Events
        subscriptionManager.subscribe(to: SessionEvent.self) { [weak self] event in
            self?.handleSessionEvent(event)
        }
        
        // System Events
        subscriptionManager.subscribe(to: SystemEvent.self) { [weak self] event in
            self?.handleSystemEvent(event)
        }
    }
    
    // MARK: - Event Handlers
    
    private func handleGameEvent(_ event: GameEvent) {
        switch event {
        case .gameStarted(let game), .gameLoaded(let game):
            updateGameState(from: game)
            
        case .gameReset:
            resetGameState()
            
        case .positionChanged(_):
            updateLastMove()
            clearSelections()
            
        case .moveIndexChanged(_):
            updateLastMove()
            clearSelections()
            
        case .gameStatusChanged(let status):
            gameStatus = status
            
        case .gameModified:
            isModified = true
        }
    }
    
    private func handleMoveEvent(_ event: MoveEvent) {
        switch event {
        case .moveExecuted(let move):
            lastMove = move
            clearSelections()
            
        case .promotionRequired(let move):
            promotionMove = move
            showPromotionDialog = true
            clearSelections()
            
        case .variationCreationRequired(let move, let fromIndex, let existingIndex):
            pendingMove = move
            pendingMoveFromIndex = fromIndex
            existingNextMoveIndex = existingIndex
            showVariationCreationDialog = true
            clearSelections()
            
        case .navigationRequested(_):
            clearSelections()
            
        case .variationSelected(_):
            showVariationSelection = false
            availableVariations = []
            
        default:
            break
        }
    }
    
    private func handleUIEvent(_ event: UIEvent) {
        switch event {
        case .squareSelected(let square):
            selectedSquare = square
            updatePossibleMoves()
            
        case .squareDeselected:
            clearSelections()
            
        case .boardFlipped(let flipped):
            isBoardFlipped = flipped
            
        case .reverseDragStarted(let target):
            isReverseDragActive = true
            reverseDragTarget = target
            updateReverseDragSources()
            
        case .reverseDragEnded:
            clearReverseDrag()
            
        case .annotationAdded(_), .annotationRemoved(_):
            isModified = true
            
        default:
            break
        }
    }
    
    private func handleEngineEvent(_ event: EngineEvent) {
        switch event {
        case .engineStarted:
            engineState = .idle
            
        case .engineStopped:
            engineState = .stopped
            currentEngineEvaluation = nil
            engineLines = []
            
        case .analysisStarted(_):
            engineState = .analyzing
            
        case .analysisUpdated(let evaluation, let lines):
            currentEngineEvaluation = evaluation
            engineLines = lines
            
        case .analysisPaused:
            engineState = .paused
            
        case .analysisResumed:
            engineState = .analyzing
            
        case .engineError(_):
            // Handle engine errors
            break
        }
    }
    
    private func handleFileEvent(_ event: FileEvent) {
        switch event {
        case .fileLoaded(let url), .fileSaved(let url):
            currentFilePath = url
            isModified = false
            
        case .fileImported(_):
            isModified = false
            
        case .fileExported(_):
            // Export doesn't change modification state
            break
            
        case .fileError(_):
            // Handle file errors
            break
        }
    }
    
    private func handleSessionEvent(_ event: SessionEvent) {
        switch event {
        case .sessionActivated(let session):
            currentSession = session
            updateFromSession(session)
            
        case .sessionDeactivated(_):
            // Keep current session reference but could add logic here
            break
            
        default:
            break
        }
    }
    
    private func handleSystemEvent(_ event: SystemEvent) {
        switch event {
        case .stateReset:
            resetAllState()

        case .cacheInvalidated:
            // Handle cache invalidation if needed
            break

        case .errorOccurred(_), .debugMessage(_):
            // Handle system messages
            break
        }
    }

    // MARK: - State Update Helpers

    private func updateGameState(from game: Game) {
        // Update game-related state based on the new game
        if let session = currentSession {
            updateFromSession(session)
        }
    }

    private func updateFromSession(_ session: GameSession) {
        // Update state from session
        isBoardFlipped = session.isBoardFlipped
        currentAnnotationColor = session.currentAnnotationColor
        currentFilePath = session.currentFilePath
        isModified = session.isModified
        updateLastMove()
    }

    private func updateLastMove() {
        guard let session = currentSession else {
            lastMove = nil
            return
        }

        if session.currentMoveIndex != session.game.startingIndex {
            lastMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
        } else {
            lastMove = nil
        }
    }

    private func updatePossibleMoves() {
        guard let session = currentSession,
              let square = selectedSquare else {
            possibleMoves = []
            return
        }

        possibleMoves = session.board.legalMoves(forPieceAt: square)
    }

    private func updateReverseDragSources() {
        guard let session = currentSession,
              let target = reverseDragTarget else {
            reverseDragValidSources = []
            return
        }

        // Find all pieces that can move to the target square
        let allSquares = Square.allCases
        let validSources = allSquares.filter { square in
            guard let piece = session.board.position.piece(at: square),
                  piece.color == session.board.position.sideToMove else { return false }
            return session.board.canMove(pieceAt: square, to: target)
        }

        reverseDragValidSources = validSources
    }

    private func clearSelections() {
        selectedSquare = nil
        possibleMoves = []
    }

    private func clearReverseDrag() {
        isReverseDragActive = false
        reverseDragTarget = nil
        reverseDragValidSources = []
    }

    private func resetGameState() {
        lastMove = nil
        gameStatus = .inProgress
        clearSelections()
        clearReverseDrag()
        clearDialogs()
    }

    private func clearDialogs() {
        showPromotionDialog = false
        showVariationCreationDialog = false
        showVariationSelection = false
        promotionMove = nil
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        availableVariations = []
        isKeyboardNavigationDisabled = false
    }

    private func resetAllState() {
        resetGameState()
        currentSession = nil
        isBoardFlipped = false
        currentAnnotationColor = .green
        currentFilePath = nil
        isModified = false
        engineState = .stopped
        currentEngineEvaluation = nil
        engineLines = []
    }

    // MARK: - Public State Access

    /// Computed properties for UI binding
    var canGoToPreviousMove: Bool {
        currentSession?.canGoToPreviousMove ?? false
    }

    var canGoToNextMove: Bool {
        currentSession?.canGoToNextMove ?? false
    }

    var currentPlayer: Piece.Color {
        currentSession?.board.position.sideToMove ?? .white
    }

    var pgn: String {
        currentSession?.pgn ?? ""
    }

    var isEngineRunning: Bool {
        engineState != .stopped
    }

    var isEngineAnalyzing: Bool {
        engineState == .analyzing
    }

    var isEnginePaused: Bool {
        engineState == .paused
    }

    // MARK: - State Mutation Methods

    /// Updates the current session
    func setCurrentSession(_ session: GameSession) {
        currentSession = session
        updateFromSession(session)
        eventBus.publish(SessionEvent.sessionActivated(session))
    }

    /// Updates board flip state
    func setBoardFlipped(_ flipped: Bool) {
        isBoardFlipped = flipped
        currentSession?.isBoardFlipped = flipped
        eventBus.publish(UIEvent.boardFlipped(flipped))
    }

    /// Updates annotation color
    func setAnnotationColor(_ color: Move.VisualAnnotations.AnnotationColor) {
        currentAnnotationColor = color
        currentSession?.currentAnnotationColor = color
    }

    /// Clears all UI selections
    func clearAllSelections() {
        clearSelections()
        clearReverseDrag()
        eventBus.publish(UIEvent.squareDeselected)
    }
}

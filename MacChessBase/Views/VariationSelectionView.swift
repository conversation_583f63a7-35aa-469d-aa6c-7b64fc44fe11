//
//  VariationSelectionViewNew.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit

/// A view for selecting between multiple variations when navigating moves
struct VariationSelectionView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    @State private var selectedIndex = 0
    @FocusState private var isFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            headerView
            variationListView
        }
        .frame(width: 400, height: 300)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
        .onAppear {
            isFocused = true
        }
        .focused($isFocused)
        .onKeyPress(.upArrow) {
            selectPrevious()
            return .handled
        }
        .onKeyPress(.downArrow) {
            selectNext()
            return .handled
        }
        .onKeyPress(.return) {
            confirmSelection()
            return .handled
        }
        .onKeyPress(.escape) {
            cancelSelection()
            return .handled
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            Text("Select Variation")
                .font(.title2)
                .fontWeight(.semibold)
            
            keyboardShortcutsView
        }
        .padding(.top, 20)
        .padding(.horizontal, 20)
    }
    
    private var keyboardShortcutsView: some View {
        HStack(spacing: 12) {
            shortcutView(keys: "↑↓", description: "Select", color: .blue)
            shortcutView(keys: "Enter", description: "Confirm", color: .green)
            shortcutView(keys: "Esc", description: "Cancel", color: .red)
        }
        .font(.caption2)
    }
    
    private func shortcutView(keys: String, description: String, color: Color) -> some View {
        HStack(spacing: 3) {
            Text(keys)
                .font(.system(.caption2, design: .monospaced))
                .foregroundColor(color.opacity(0.8))
                .padding(.horizontal, 4)
                .padding(.vertical, 1)
                .background(color.opacity(0.1))
                .cornerRadius(3)
            Text(description)
                .foregroundColor(.secondary)
        }
    }
    
    private var variationListView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(Array(viewModel.availableVariations.enumerated()), id: \.element.id) { index, variation in
                    variationRow(variation: variation, index: index)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    private func variationRow(variation: VariationOption, index: Int) -> some View {
        HStack {
            Text(variation.pgnText)
                .font(.system(.body, design: .monospaced))
                .foregroundColor(variation.isMainLine ? .primary : .secondary)
            Spacer()
            if variation.isMainLine {
                Text("Main")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(4)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(selectedIndex == index ? Color.accentColor.opacity(0.2) : Color.clear)
        .cornerRadius(6)
        .onTapGesture {
            selectedIndex = index
            confirmSelection()
        }
    }
    
    // MARK: - Actions
    
    private func selectPrevious() {
        if selectedIndex > 0 {
            selectedIndex -= 1
        }
    }
    
    private func selectNext() {
        if selectedIndex < viewModel.availableVariations.count - 1 {
            selectedIndex += 1
        }
    }
    
    private func confirmSelection() {
        if !viewModel.availableVariations.isEmpty && selectedIndex < viewModel.availableVariations.count {
            viewModel.selectVariation(viewModel.availableVariations[selectedIndex])
        }
    }
    
    private func cancelSelection() {
        viewModel.cancelVariationSelection()
    }
}

#Preview {
    // Create a mock view model for preview
    let session = GameSession()
    let viewModel = ChessGameViewModel(session: session)
    
    return VariationSelectionView(viewModel: viewModel)
        .frame(width: 500, height: 400)
}
